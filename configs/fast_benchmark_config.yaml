# Fast benchmark configuration for performance-optimized experiments
# This configuration reduces computational complexity while maintaining experimental validity

# Random seed for reproducibility
random_state: 42

# Data processing settings
data:
  train_split: 0.7
  val_split: 0.1
  test_split: 0.2
  # Limit dataset size for faster processing
  max_samples_per_dataset: 5000  # Reduced from full datasets
  
# Preprocessing configuration
preprocessing:
  normalization_method: "minmax"
  handle_missing: "interpolate"
  outlier_detection: false  # Disabled for speed
  feature_engineering: false  # Disabled for speed

# Clustering configuration (optimized for speed)
clustering:
  n_clusters: 4  # Reduced from 5 for faster computation
  fcm_fuzziness: 2.0
  max_iter: 50  # Reduced from 100
  tol: 0.01  # Relaxed tolerance for faster convergence
  
# Neutrosophic transformation settings
neutrosophic:
  entropy_base: 2
  entropy_epsilon: 1e-10
  components: ["truth", "indeterminacy", "falsity"]
  
# Random Forest configuration (optimized for speed)
random_forest:
  n_estimators: 50  # Reduced from 100
  max_depth: 10  # Limited depth for faster training
  min_samples_split: 5  # Increased for faster training
  min_samples_leaf: 2  # Increased for faster training
  max_features: "sqrt"
  bootstrap: true
  n_jobs: -1  # Use all available cores
  
# Forecasting parameters
forecasting:
  gamma: 1.96
  beta: 1.0
  confidence_level: 0.95
  
# Evaluation settings
evaluation:
  metrics: ["rmse", "mae", "mape", "r2"]
  confidence_level: 0.95
  
# Performance optimization settings
performance:
  use_parallel: true
  max_workers: 4  # Limit parallel workers to avoid memory issues
  batch_size: 50  # For vectorized predictions
  enable_caching: true
  
# Statistical testing
statistical_tests:
  alpha: 0.05
  tests: ["wilcoxon", "friedman"]
  
# Experiment configuration
experiment:
  n_runs: 1  # Single run for speed
  cross_validation: false  # Disabled for speed
  save_intermediate_results: false  # Disabled for speed
  
# Logging configuration
logging:
  level: "INFO"
  save_logs: true
  log_performance_metrics: true
