#!/usr/bin/env python3
"""
Debug script to isolate the neutrosophic transformation issue.
"""

import sys
import numpy as np
import pandas as pd
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent))

from src.clustering.dual_clusterer import DualClusterer
from src.neutrosophic.neutrosophic_transformer import NeutrosophicTransformer
from src.utils.logger import setup_logger

def debug_neutrosophic_issue():
    """Debug the neutrosophic transformation issue step by step."""
    logger = setup_logger("debug_neutrosophic")
    
    print("=== DEBUGGING NEUTROSOPHIC TRANSFORMATION ===")
    
    # Step 1: Create simple test data
    print("\nStep 1: Creating test data...")
    np.random.seed(42)
    n_samples = 50
    X = np.random.randn(n_samples, 1)
    print(f"Test data shape: {X.shape}, dtype: {X.dtype}")
    
    # Step 2: Test dual clustering
    print("\nStep 2: Testing dual clustering...")
    dual_clusterer = DualClusterer(n_clusters=3, max_iter=20, tol=0.1)
    dual_clusterer.fit(X)
    
    # Get cluster assignments
    kmeans_labels, fcm_memberships = dual_clusterer.get_cluster_assignments()
    print(f"K-means labels shape: {kmeans_labels.shape}, dtype: {kmeans_labels.dtype}")
    print(f"FCM memberships shape: {fcm_memberships.shape}, dtype: {fcm_memberships.dtype}")
    print(f"K-means labels sample: {kmeans_labels[:5]}")
    print(f"FCM memberships sample: {fcm_memberships[:2]}")
    
    # Step 3: Test integrated features
    print("\nStep 3: Testing integrated features...")
    integrated_features = dual_clusterer.get_integrated_features()
    print(f"Integrated features shape: {integrated_features.shape}, dtype: {integrated_features.dtype}")
    print(f"Integrated features sample: {integrated_features[:2]}")
    
    # Step 4: Test neutrosophic transformation
    print("\nStep 4: Testing neutrosophic transformation...")
    neutrosophic_transformer = NeutrosophicTransformer()
    
    try:
        components = neutrosophic_transformer.transform(kmeans_labels, fcm_memberships)
        print(f"✓ Neutrosophic transformation successful!")
        print(f"Truth shape: {components.truth.shape}, dtype: {components.truth.dtype}")
        print(f"Indeterminacy shape: {components.indeterminacy.shape}, dtype: {components.indeterminacy.dtype}")
        print(f"Falsity shape: {components.falsity.shape}, dtype: {components.falsity.dtype}")
        
        # Step 5: Test feature enrichment
        print("\nStep 5: Testing feature enrichment...")
        enriched_features = neutrosophic_transformer.create_enriched_features(
            X, integrated_features, components
        )
        print(f"✓ Feature enrichment successful!")
        print(f"Enriched features shape: {enriched_features.shape}, dtype: {enriched_features.dtype}")
        
        return True
        
    except Exception as e:
        print(f"✗ Neutrosophic transformation failed: {e}")
        
        # Debug the inputs
        print(f"\nDebugging inputs:")
        print(f"K-means labels type: {type(kmeans_labels)}")
        print(f"K-means labels dtype: {kmeans_labels.dtype}")
        print(f"K-means labels content: {kmeans_labels}")
        print(f"FCM memberships type: {type(fcm_memberships)}")
        print(f"FCM memberships dtype: {fcm_memberships.dtype}")
        print(f"FCM memberships content:\n{fcm_memberships}")
        
        # Check for mixed types
        print(f"\nChecking for mixed types in arrays:")
        for i, label in enumerate(kmeans_labels.flatten()[:10]):
            print(f"  kmeans_labels[{i}]: {label} (type: {type(label)})")
        
        for i in range(min(3, fcm_memberships.shape[0])):
            for j in range(fcm_memberships.shape[1]):
                val = fcm_memberships[i, j]
                print(f"  fcm_memberships[{i},{j}]: {val} (type: {type(val)})")
        
        return False

if __name__ == "__main__":
    success = debug_neutrosophic_issue()
    if success:
        print("\n🎉 All tests passed!")
    else:
        print("\n❌ Debug revealed issues that need fixing.")
    sys.exit(0 if success else 1)