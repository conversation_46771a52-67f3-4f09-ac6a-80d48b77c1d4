#!/usr/bin/env python3
"""
Test script to validate critical bug fixes before running experiments.
This script tests the key components that were causing experiment failures.
"""

import sys
import numpy as np
import pandas as pd
from pathlib import Path
import logging

# Add src to path
sys.path.append(str(Path(__file__).parent))

from src.neutrosophic.neutrosophic_transformer import NeutrosophicTransformer
from src.clustering.dual_clusterer import DualClusterer
from src.framework.forecasting_framework import NeutrosophicForecastingFramework

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_neutrosophic_transformer():
    """Test the neutrosophic transformer with mixed data types."""
    logger.info("Testing neutrosophic transformer...")
    
    # Create sample data with potential type issues
    np.random.seed(42)
    n_samples = 100
    
    # Create mixed data that could cause type issues
    data = pd.DataFrame({
        'feature1': np.random.randn(n_samples),
        'feature2': np.random.randn(n_samples),
        'feature3': np.random.randn(n_samples)
    })
    
    try:
        transformer = NeutrosophicTransformer()
        transformed_data = transformer.transform(data)
        
        # Validate output
        assert isinstance(transformed_data, np.ndarray), "Output should be numpy array"
        assert transformed_data.dtype in [np.float32, np.float64], f"Output should be numeric, got {transformed_data.dtype}"
        assert not np.any(np.isnan(transformed_data)), "Output should not contain NaN values"
        
        logger.info("✅ Neutrosophic transformer test passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Neutrosophic transformer test failed: {e}")
        return False

def test_dual_clusterer():
    """Test the dual clusterer with configuration parameters."""
    logger.info("Testing dual clusterer...")
    
    try:
        # Create sample data
        np.random.seed(42)
        data = np.random.randn(100, 5)
        
        # Test with default configuration
        clusterer = DualClusterer(n_clusters=3)
        labels = clusterer.fit_predict(data)
        
        # Validate output
        assert isinstance(labels, np.ndarray), "Labels should be numpy array"
        assert len(labels) == len(data), "Labels length should match data length"
        assert labels.dtype in [np.int32, np.int64], f"Labels should be integer, got {labels.dtype}"
        
        logger.info("✅ Dual clusterer test passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Dual clusterer test failed: {e}")
        return False

def test_framework_integration():
    """Test the overall framework integration."""
    logger.info("Testing framework integration...")
    
    try:
        # Create sample time series data
        np.random.seed(42)
        n_samples = 50  # Small sample for quick test
        
        timestamps = pd.date_range('2023-01-01', periods=n_samples, freq='H')
        data = pd.DataFrame({
            'timestamp': timestamps,
            'energy_generation': np.random.uniform(0, 100, n_samples)
        })
        
        # Test framework initialization
        config = {
            'clustering': {'n_clusters': 3, 'max_iter': 10},
            'neutrosophic': {'alpha': 0.5},
            'model': {'n_estimators': 10, 'max_depth': 3}
        }
        
        framework = NeutrosophicForecastingFramework(config)
        
        # Test basic functionality (without full training)
        logger.info("✅ Framework integration test passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Framework integration test failed: {e}")
        return False

def main():
    """Run all bug fix validation tests."""
    logger.info("=== Starting Bug Fix Validation Tests ===")
    
    tests = [
        test_neutrosophic_transformer,
        test_dual_clusterer,
        test_framework_integration
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    logger.info(f"=== Test Results: {passed}/{total} tests passed ===")
    
    if passed == total:
        logger.info("✅ All bug fix tests passed! Ready to run experiments.")
        return 0
    else:
        logger.error("❌ Some tests failed! Please check the fixes before running experiments.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
