Modules Release 5.3.0 (2023-05-14)
DCC-SW: Added modules (2023-aug/XeonGold6226R)
DCC-SW: Removed standard modules: /apps/dcc/etc/ModulesAlma92/modulefiles
DCC-SW: Using gnu/12.3.0 compiler (requested)
Modules Release 4.8.0 (2021-07-14)
DCC-SW: Added modules (2023-aug/XeonGold6226R)
DCC-SW: Removed standard modules: /apps/dcc/etc/ModulesAlma92/modulefiles
DCC-SW: Using gnu/12.3.0 compiler (requested)
Loaded module: cuda/12.6
INFO:__main__:=== Starting Bug Fix Validation Tests ===
INFO:__main__:Testing neutrosophic transformer...
ERROR:__main__:❌ Neutrosophic transformer test failed: NeutrosophicTransformer.transform() missing 1 required positional argument: 'fcm_memberships'
INFO:__main__:Testing dual clusterer...
INFO:src.clustering.dual_clusterer:Fitting dual clustering on data shape (100, 5)
INFO:src.clustering.dual_clusterer:Fitting K-Means clusterer
INFO:src.clustering.kmeans_clusterer:Fitting K-Means with 3 clusters on data shape (100, 5)
INFO:src.clustering.kmeans_clusterer:K-Means fitting completed. Inertia: 336.6258
INFO:src.clustering.dual_clusterer:Fitting FCM clusterer
INFO:src.clustering.fcm_clusterer:Fitting FCM with 3 clusters, m=2.0 on data shape (100, 5)
ERROR:src.clustering.fcm_clusterer:FCM fit received X with shape (100, 5). Expected (n_samples,) or (n_samples, 1).
ERROR:src.clustering.dual_clusterer:Failed to fit dual clusterer: FCMClusterer expects single-feature input (n_samples,) or (n_samples, 1).
WARNING:src.clustering.dual_clusterer:Attempting to fit with relaxed parameters
INFO:src.clustering.kmeans_clusterer:Fitting K-Means with 3 clusters on data shape (100, 5)
INFO:src.clustering.kmeans_clusterer:K-Means fitting completed. Inertia: 336.2359
INFO:src.clustering.fcm_clusterer:Fitting FCM with 3 clusters, m=2.0 on data shape (100, 5)
ERROR:src.clustering.fcm_clusterer:FCM fit received X with shape (100, 5). Expected (n_samples,) or (n_samples, 1).
ERROR:src.clustering.dual_clusterer:Failed to fit dual clusterer even with relaxed parameters: FCMClusterer expects single-feature input (n_samples,) or (n_samples, 1).
ERROR:__main__:❌ Dual clusterer test failed: Dual clusterer fitting failed: FCMClusterer expects single-feature input (n_samples,) or (n_samples, 1).
INFO:__main__:Testing framework integration...
INFO:src.utils.math_utils:Set random seeds to 42
ERROR:__main__:❌ Framework integration test failed: NeutrosophicTransformer.__init__() got an unexpected keyword argument 'alpha'
INFO:__main__:=== Test Results: 0/3 tests passed ===
ERROR:__main__:❌ Some tests failed! Please check the fixes before running experiments.
