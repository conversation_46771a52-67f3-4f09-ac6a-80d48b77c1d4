Modules Release 5.3.0 (2023-05-14)
DCC-SW: Added modules (2023-aug/XeonGold6226R)
DCC-SW: Removed standard modules: /apps/dcc/etc/ModulesAlma92/modulefiles
DCC-SW: Using gnu/12.3.0 compiler (requested)
Modules Release 4.8.0 (2021-07-14)
DCC-SW: Added modules (2023-aug/XeonGold6226R)
DCC-SW: Removed standard modules: /apps/dcc/etc/ModulesAlma92/modulefiles
DCC-SW: Using gnu/12.3.0 compiler (requested)
Loaded module: cuda/12.6
INFO:__main__:=== Starting Bug Fix Validation Tests ===
INFO:__main__:Testing neutrosophic transformer...
INFO:src.neutrosophic.neutrosophic_transformer:Starting neutrosophic transformation with robust data type handling
INFO:src.neutrosophic.neutrosophic_transformer:Transforming dual clustering outputs to neutrosophic components for 100 samples
INFO:src.neutrosophic.neutrosophic_transformer:Neutrosophic transformation completed
INFO:src.neutrosophic.neutrosophic_transformer:Truth range: [0.004, 0.968]
INFO:src.neutrosophic.neutrosophic_transformer:Indeterminacy range: [0.144, 0.997]
INFO:src.neutrosophic.neutrosophic_transformer:Falsity range: [0.032, 0.996]
INFO:__main__:✅ Neutrosophic transformer test passed
INFO:__main__:Testing dual clusterer...
INFO:src.clustering.dual_clusterer:Fitting dual clustering on data shape (100, 5)
INFO:src.clustering.dual_clusterer:Fitting K-Means clusterer
INFO:src.clustering.kmeans_clusterer:Fitting K-Means with 3 clusters on data shape (100, 5)
INFO:src.clustering.kmeans_clusterer:K-Means fitting completed. Inertia: 336.6258
INFO:src.clustering.dual_clusterer:Fitting FCM clusterer
INFO:src.clustering.fcm_clusterer:Fitting FCM with 3 clusters, m=2.0 on data shape (100, 5)
WARNING:src.clustering.fcm_clusterer:FCM did not converge after 100 iterations
INFO:src.clustering.fcm_clusterer:FCM fitting completed. Final objective: 158.9775
INFO:src.clustering.dual_clusterer:Created integrated features with shape (100, 6) and dtype float64
INFO:src.clustering.dual_clusterer:Dual clustering fitting completed
INFO:__main__:✅ Dual clusterer test passed
INFO:__main__:Testing framework integration...
INFO:src.utils.math_utils:Set random seeds to 42
INFO:NeutrosophicForecastingFramework:Neutrosophic Forecasting Framework initialized
INFO:__main__:✅ Framework integration test passed
INFO:__main__:=== Test Results: 3/3 tests passed ===
INFO:__main__:✅ All bug fix tests passed! Ready to run experiments.
Data type mismatch error in proposed model: Neutrosophic transformation stage failed: ufunc 'add' did not contain a loop with signature matching types (dtype('float64'), dtype('<U4')) -> None
This indicates mixed data types (float64 and string) in array operations
Train data info: shape=(55104, 26), columns=['energy_generation', 'temperature', 'hour', 'day_of_week', 'day_of_year', 'month', 'quarter', 'is_weekend', 'hour_sin', 'hour_cos', 'day_sin', 'day_cos', 'month_sin', 'month_cos', 'energy_generation_lag_1', 'energy_generation_lag_2', 'energy_generation_lag_3', 'energy_generation_lag_6', 'energy_generation_lag_12', 'energy_generation_lag_24', 'energy_generation_lag_48', 'energy_generation_lag_168', 'energy_generation_rolling_mean_24', 'energy_generation_rolling_std_24', 'energy_generation_rolling_mean_168', 'energy_generation_rolling_std_168']
Test data info: shape=(15744, 26), columns=['energy_generation', 'temperature', 'hour', 'day_of_week', 'day_of_year', 'month', 'quarter', 'is_weekend', 'hour_sin', 'hour_cos', 'day_sin', 'day_cos', 'month_sin', 'month_cos', 'energy_generation_lag_1', 'energy_generation_lag_2', 'energy_generation_lag_3', 'energy_generation_lag_6', 'energy_generation_lag_12', 'energy_generation_lag_24', 'energy_generation_lag_48', 'energy_generation_lag_168', 'energy_generation_rolling_mean_24', 'energy_generation_rolling_std_24', 'energy_generation_rolling_mean_168', 'energy_generation_rolling_std_168']
Data type mismatch error in proposed model: Neutrosophic transformation stage failed: ufunc 'add' did not contain a loop with signature matching types (dtype('float64'), dtype('<U4')) -> None
This indicates mixed data types (float64 and string) in array operations
Train data info: shape=(2093, 27), columns=['energy_generation', 'dc_power', 'daily_yield', 'hour', 'day_of_week', 'day_of_year', 'month', 'quarter', 'is_weekend', 'hour_sin', 'hour_cos', 'day_sin', 'day_cos', 'month_sin', 'month_cos', 'energy_generation_lag_1', 'energy_generation_lag_2', 'energy_generation_lag_3', 'energy_generation_lag_6', 'energy_generation_lag_12', 'energy_generation_lag_24', 'energy_generation_lag_48', 'energy_generation_lag_168', 'energy_generation_rolling_mean_24', 'energy_generation_rolling_std_24', 'energy_generation_rolling_mean_168', 'energy_generation_rolling_std_168']
Test data info: shape=(598, 27), columns=['energy_generation', 'dc_power', 'daily_yield', 'hour', 'day_of_week', 'day_of_year', 'month', 'quarter', 'is_weekend', 'hour_sin', 'hour_cos', 'day_sin', 'day_cos', 'month_sin', 'month_cos', 'energy_generation_lag_1', 'energy_generation_lag_2', 'energy_generation_lag_3', 'energy_generation_lag_6', 'energy_generation_lag_12', 'energy_generation_lag_24', 'energy_generation_lag_48', 'energy_generation_lag_168', 'energy_generation_rolling_mean_24', 'energy_generation_rolling_std_24', 'energy_generation_rolling_mean_168', 'energy_generation_rolling_std_168']
Data type mismatch error in proposed model: Neutrosophic transformation stage failed: ufunc 'add' did not contain a loop with signature matching types (dtype('float64'), dtype('<U4')) -> None
This indicates mixed data types (float64 and string) in array operations
Train data info: shape=(11559, 33), columns=['energy_generation', 'wind_u10', 'wind_v10', 'wind_u100', 'wind_v100', 'wind_speed_10m', 'wind_speed_100m', 'wind_direction_10m', 'wind_direction_100m', 'hour', 'day_of_week', 'day_of_year', 'month', 'quarter', 'is_weekend', 'hour_sin', 'hour_cos', 'day_sin', 'day_cos', 'month_sin', 'month_cos', 'energy_generation_lag_1', 'energy_generation_lag_2', 'energy_generation_lag_3', 'energy_generation_lag_6', 'energy_generation_lag_12', 'energy_generation_lag_24', 'energy_generation_lag_48', 'energy_generation_lag_168', 'energy_generation_rolling_mean_24', 'energy_generation_rolling_std_24', 'energy_generation_rolling_mean_168', 'energy_generation_rolling_std_168']
Test data info: shape=(3303, 33), columns=['energy_generation', 'wind_u10', 'wind_v10', 'wind_u100', 'wind_v100', 'wind_speed_10m', 'wind_speed_100m', 'wind_direction_10m', 'wind_direction_100m', 'hour', 'day_of_week', 'day_of_year', 'month', 'quarter', 'is_weekend', 'hour_sin', 'hour_cos', 'day_sin', 'day_cos', 'month_sin', 'month_cos', 'energy_generation_lag_1', 'energy_generation_lag_2', 'energy_generation_lag_3', 'energy_generation_lag_6', 'energy_generation_lag_12', 'energy_generation_lag_24', 'energy_generation_lag_48', 'energy_generation_lag_168', 'energy_generation_rolling_mean_24', 'energy_generation_rolling_std_24', 'energy_generation_rolling_mean_168', 'energy_generation_rolling_std_168']
Data type mismatch error in proposed model: Neutrosophic transformation stage failed: ufunc 'add' did not contain a loop with signature matching types (dtype('float64'), dtype('<U4')) -> None
This indicates mixed data types (float64 and string) in array operations
Train data info: shape=(18379, 8), columns=['energy_generation', 'hour', 'day_of_week', 'month', 'day_of_year', 'cloud_cover', 'temperature', 'humidity']
Test data info: shape=(5253, 8), columns=['energy_generation', 'hour', 'day_of_week', 'month', 'day_of_year', 'cloud_cover', 'temperature', 'humidity']
Data type mismatch error in proposed model: Neutrosophic transformation stage failed: ufunc 'add' did not contain a loop with signature matching types (dtype('float64'), dtype('<U4')) -> None
This indicates mixed data types (float64 and string) in array operations
Train data info: shape=(12180, 28), columns=['energy_generation', 'generation_lcl', 'generation_ucl', 'generation_uncertainty', 'hour', 'day_of_week', 'day_of_year', 'month', 'quarter', 'is_weekend', 'hour_sin', 'hour_cos', 'day_sin', 'day_cos', 'month_sin', 'month_cos', 'energy_generation_lag_1', 'energy_generation_lag_2', 'energy_generation_lag_3', 'energy_generation_lag_6', 'energy_generation_lag_12', 'energy_generation_lag_24', 'energy_generation_lag_48', 'energy_generation_lag_168', 'energy_generation_rolling_mean_24', 'energy_generation_rolling_std_24', 'energy_generation_rolling_mean_168', 'energy_generation_rolling_std_168']
Test data info: shape=(3480, 28), columns=['energy_generation', 'generation_lcl', 'generation_ucl', 'generation_uncertainty', 'hour', 'day_of_week', 'day_of_year', 'month', 'quarter', 'is_weekend', 'hour_sin', 'hour_cos', 'day_sin', 'day_cos', 'month_sin', 'month_cos', 'energy_generation_lag_1', 'energy_generation_lag_2', 'energy_generation_lag_3', 'energy_generation_lag_6', 'energy_generation_lag_12', 'energy_generation_lag_24', 'energy_generation_lag_48', 'energy_generation_lag_168', 'energy_generation_rolling_mean_24', 'energy_generation_rolling_std_24', 'energy_generation_rolling_mean_168', 'energy_generation_rolling_std_168']
Data type mismatch error in proposed model: Neutrosophic transformation stage failed: ufunc 'add' did not contain a loop with signature matching types (dtype('float64'), dtype('<U4')) -> None
This indicates mixed data types (float64 and string) in array operations
Train data info: shape=(29165, 8), columns=['energy_generation', 'hour', 'day_of_week', 'month', 'day_of_year', 'cloud_cover', 'temperature', 'humidity']
Test data info: shape=(8334, 8), columns=['energy_generation', 'hour', 'day_of_week', 'month', 'day_of_year', 'cloud_cover', 'temperature', 'humidity']
Ablation full_model failed: Neutrosophic transformation stage failed: ufunc 'add' did not contain a loop with signature matching types (dtype('float64'), dtype('<U4')) -> None
Ablation without_neutrosophic failed: Neutrosophic transformation stage failed: ufunc 'add' did not contain a loop with signature matching types (dtype('float64'), dtype('<U4')) -> None
Ablation kmeans_only failed: Neutrosophic transformation stage failed: ufunc 'add' did not contain a loop with signature matching types (dtype('float64'), dtype('<U4')) -> None
Ablation fcm_only failed: Neutrosophic transformation stage failed: ufunc 'add' did not contain a loop with signature matching types (dtype('float64'), dtype('<U4')) -> None
Ablation without_indeterminacy failed: Neutrosophic transformation stage failed: ufunc 'add' did not contain a loop with signature matching types (dtype('float64'), dtype('<U4')) -> None
Ablation distance_indeterminacy failed: Neutrosophic transformation stage failed: ufunc 'add' did not contain a loop with signature matching types (dtype('float64'), dtype('<U4')) -> None
Sensitivity analysis for n_clusters=3 failed: Neutrosophic transformation stage failed: ufunc 'add' did not contain a loop with signature matching types (dtype('float64'), dtype('<U4')) -> None
Sensitivity analysis for n_clusters=4 failed: Neutrosophic transformation stage failed: ufunc 'add' did not contain a loop with signature matching types (dtype('float64'), dtype('<U4')) -> None
Sensitivity analysis for n_clusters=5 failed: Neutrosophic transformation stage failed: ufunc 'add' did not contain a loop with signature matching types (dtype('float64'), dtype('<U4')) -> None
Sensitivity analysis for n_clusters=6 failed: Neutrosophic transformation stage failed: ufunc 'add' did not contain a loop with signature matching types (dtype('float64'), dtype('<U4')) -> None
Sensitivity analysis for n_clusters=7 failed: Neutrosophic transformation stage failed: ufunc 'add' did not contain a loop with signature matching types (dtype('float64'), dtype('<U4')) -> None
Sensitivity analysis for n_clusters=8 failed: Neutrosophic transformation stage failed: ufunc 'add' did not contain a loop with signature matching types (dtype('float64'), dtype('<U4')) -> None
Sensitivity analysis for fcm_fuzziness=1.5 failed: Neutrosophic transformation stage failed: ufunc 'add' did not contain a loop with signature matching types (dtype('float64'), dtype('<U4')) -> None
Sensitivity analysis for fcm_fuzziness=2.0 failed: Neutrosophic transformation stage failed: ufunc 'add' did not contain a loop with signature matching types (dtype('float64'), dtype('<U4')) -> None
Sensitivity analysis for fcm_fuzziness=2.5 failed: Neutrosophic transformation stage failed: ufunc 'add' did not contain a loop with signature matching types (dtype('float64'), dtype('<U4')) -> None
Sensitivity analysis for fcm_fuzziness=3.0 failed: Neutrosophic transformation stage failed: ufunc 'add' did not contain a loop with signature matching types (dtype('float64'), dtype('<U4')) -> None
Sensitivity analysis for n_estimators=50 failed: Neutrosophic transformation stage failed: ufunc 'add' did not contain a loop with signature matching types (dtype('float64'), dtype('<U4')) -> None
Sensitivity analysis for n_estimators=100 failed: Neutrosophic transformation stage failed: ufunc 'add' did not contain a loop with signature matching types (dtype('float64'), dtype('<U4')) -> None
Sensitivity analysis for n_estimators=150 failed: Neutrosophic transformation stage failed: ufunc 'add' did not contain a loop with signature matching types (dtype('float64'), dtype('<U4')) -> None
Sensitivity analysis for n_estimators=200 failed: Neutrosophic transformation stage failed: ufunc 'add' did not contain a loop with signature matching types (dtype('float64'), dtype('<U4')) -> None
Sensitivity analysis for gamma=1.0 failed: Neutrosophic transformation stage failed: ufunc 'add' did not contain a loop with signature matching types (dtype('float64'), dtype('<U4')) -> None
Sensitivity analysis for gamma=1.5 failed: Neutrosophic transformation stage failed: ufunc 'add' did not contain a loop with signature matching types (dtype('float64'), dtype('<U4')) -> None
Sensitivity analysis for gamma=1.96 failed: Neutrosophic transformation stage failed: ufunc 'add' did not contain a loop with signature matching types (dtype('float64'), dtype('<U4')) -> None
Sensitivity analysis for gamma=2.0 failed: Neutrosophic transformation stage failed: ufunc 'add' did not contain a loop with signature matching types (dtype('float64'), dtype('<U4')) -> None
Sensitivity analysis for gamma=2.5 failed: Neutrosophic transformation stage failed: ufunc 'add' did not contain a loop with signature matching types (dtype('float64'), dtype('<U4')) -> None
Sensitivity analysis for beta=0.5 failed: Neutrosophic transformation stage failed: ufunc 'add' did not contain a loop with signature matching types (dtype('float64'), dtype('<U4')) -> None
Sensitivity analysis for beta=1.0 failed: Neutrosophic transformation stage failed: ufunc 'add' did not contain a loop with signature matching types (dtype('float64'), dtype('<U4')) -> None
Sensitivity analysis for beta=1.5 failed: Neutrosophic transformation stage failed: ufunc 'add' did not contain a loop with signature matching types (dtype('float64'), dtype('<U4')) -> None
Sensitivity analysis for beta=2.0 failed: Neutrosophic transformation stage failed: ufunc 'add' did not contain a loop with signature matching types (dtype('float64'), dtype('<U4')) -> None
FCM did not converge after 300 iterations
Computational analysis for NDC-RF with size 1000 failed: Neutrosophic transformation stage failed: ufunc 'add' did not contain a loop with signature matching types (dtype('float64'), dtype('<U4')) -> None
Computational analysis for NDC-RF with size 5000 failed: Neutrosophic transformation stage failed: ufunc 'add' did not contain a loop with signature matching types (dtype('float64'), dtype('<U4')) -> None
Computational analysis for NDC-RF with size 10000 failed: Neutrosophic transformation stage failed: ufunc 'add' did not contain a loop with signature matching types (dtype('float64'), dtype('<U4')) -> None
Computational analysis for NDC-RF with size 20000 failed: Neutrosophic transformation stage failed: ufunc 'add' did not contain a loop with signature matching types (dtype('float64'), dtype('<U4')) -> None
User defined signal 2
